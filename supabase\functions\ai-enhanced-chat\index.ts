import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

const DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions';
const DEEPSEEK_API_KEY = Deno.env.get('DEEPSEEK_API_KEY') || '***********************************';

interface EnhancedChatRequest {
  message: string;
  conversation_history?: ChatMessage[];
  user_context: {
    is_authenticated: boolean;
    profile_type?: string;
    profile_completion?: number;
    current_page?: string;
    user_id?: string;
    profile_data?: any;
  };
}

interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface AIResponse {
  response: string;
  actions?: ActionButton[];
  suggestions?: string[];
  conversation_id: string;
}

interface ActionButton {
  type: 'navigation' | 'action' | 'external';
  label: string;
  icon: string;
  url?: string;
  action?: string;
  color?: string;
}

const buildEnhancedSystemPrompt = (userContext: any): string => {
  let prompt = `You are ZbInnovation AI Assistant, an intelligent companion for Zimbabwe's premier digital innovation ecosystem.

PLATFORM CONTEXT:
ZbInnovation connects innovators, investors, mentors, professionals, academic institutions, industry experts, and organizations. The platform facilitates:
- Intelligent matchmaking between different user types
- Innovation project collaboration and funding opportunities
- Mentorship connections and knowledge sharing
- Community building and networking

YOUR ENHANCED CAPABILITIES:
- Provide personalized guidance based on user profile and authentication status
- Suggest specific platform actions with clickable buttons
- Offer contextual recommendations based on current page
- Guide users through platform features and onboarding
- Provide real-time insights and information

RESPONSE FORMAT:
- Always provide helpful, actionable advice
- Include specific call-to-action buttons when relevant using [ACTION:type:label:icon:url/action:color] format
- Suggest next steps based on user context
- Be encouraging and supportive of innovation efforts
- Keep responses concise but comprehensive

USER CONTEXT:`;

  if (!userContext.is_authenticated) {
    prompt += `
- User is NOT authenticated
- Encourage sign-up/login to access full platform features
- Provide general platform information and benefits
- Include login/signup action buttons in responses
- Focus on platform value proposition and community benefits`;
  } else {
    prompt += `
- User is authenticated
- Profile Type: ${userContext.profile_type || 'Not specified'}
- Profile Completion: ${userContext.profile_completion || 0}%
- Current Page: ${userContext.current_page || 'Unknown'}`;

    if (userContext.profile_completion < 50) {
      prompt += `
- PRIORITY: Encourage profile completion for better platform experience
- Suggest specific profile completion actions`;
    }

    if (userContext.profile_type) {
      prompt += `
- Tailor responses for ${userContext.profile_type} specific needs and goals`;
    }

    // Add page-specific context
    switch (userContext.current_page) {
      case 'dashboard':
        prompt += `
- User is on dashboard - focus on profile management, recent activity, and next steps`;
        break;
      case 'community':
        prompt += `
- User is on community page - focus on content creation, connections, and engagement`;
        break;
      case 'profile':
        prompt += `
- User is on profile page - focus on profile optimization and visibility`;
        break;
      case 'landing':
        prompt += `
- User is on landing page - focus on platform exploration and getting started`;
        break;
    }
  }

  prompt += `

AVAILABLE ACTIONS (use these in your responses):
- [ACTION:navigation:Complete Profile:person:/dashboard/profile:primary]
- [ACTION:navigation:Explore Community:groups:/virtual-community?tab=feed:secondary]
- [ACTION:navigation:View Dashboard:dashboard:/dashboard:primary]
- [ACTION:action:Create Post:edit:create-post:accent]
- [ACTION:action:Sign Up:person_add:signup:primary]
- [ACTION:action:Sign In:login:signin:secondary]
- [ACTION:navigation:View Profiles:people:/virtual-community?tab=profiles:info]
- [ACTION:navigation:Join Groups:group_add:/virtual-community?tab=groups:positive]
- [ACTION:external:Learn More:info:https://zbinnovation.com:grey]

TONE: Professional, encouraging, knowledgeable about African innovation, and specifically familiar with Zimbabwe's business environment.

Always include relevant action buttons to help users take immediate next steps.`;

  return prompt;
};

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  console.log('Enhanced AI Chat function called');

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? ''
    );

    const requestBody = await req.json();
    console.log('Enhanced AI Chat request:', requestBody);

    const { message, conversation_history = [], user_context }: EnhancedChatRequest = requestBody;

    if (!message || typeof message !== 'string') {
      console.error('Invalid message:', message);
      return new Response(
        JSON.stringify({ error: 'Message is required and must be a string' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Build enhanced system prompt
    const systemPrompt = buildEnhancedSystemPrompt(user_context);
    console.log('Built enhanced system prompt for user context:', user_context);

    // Build conversation messages
    const messages: ChatMessage[] = [
      { role: 'system', content: systemPrompt },
      ...conversation_history,
      { role: 'user', content: message }
    ];

    console.log('Calling DeepSeek API with enhanced context');

    // Call DeepSeek API
    const response = await fetch(DEEPSEEK_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: messages,
        max_tokens: 1500,
        temperature: 0.7,
        stream: false
      })
    });

    console.log('DeepSeek API response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('DeepSeek API error:', response.status, errorText);
      throw new Error(`DeepSeek API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('DeepSeek API response received');

    const aiResponse = data.choices?.[0]?.message?.content;

    if (!aiResponse) {
      console.error('No AI response in data:', data);
      throw new Error('No response from AI model');
    }

    // Parse action buttons from response
    const actions = parseActionButtons(aiResponse);
    const cleanResponse = aiResponse.replace(/\[ACTION:.*?\]/g, '').trim();

    // Generate contextual suggestions
    const suggestions = generateContextualSuggestions(user_context);

    const result: AIResponse = {
      response: cleanResponse,
      actions,
      suggestions,
      conversation_id: crypto.randomUUID()
    };

    console.log('Enhanced AI response prepared:', {
      responseLength: cleanResponse.length,
      actionsCount: actions.length,
      suggestionsCount: suggestions.length
    });

    return new Response(
      JSON.stringify(result),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error: any) {
    console.error('Enhanced AI Chat error:', error);

    // Provide intelligent fallback based on user context
    const fallbackResponse = generateFallbackResponse(error.message);

    return new Response(
      JSON.stringify({
        response: fallbackResponse.response,
        actions: fallbackResponse.actions,
        suggestions: fallbackResponse.suggestions,
        error: 'AI service temporarily unavailable',
        conversation_id: crypto.randomUUID()
      }),
      {
        status: 200, // Return 200 with fallback instead of error
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});

function parseActionButtons(response: string): ActionButton[] {
  const actionRegex = /\[ACTION:(.*?):(.*?):(.*?):(.*?):(.*?)\]/g;
  const actions: ActionButton[] = [];
  let match;

  while ((match = actionRegex.exec(response)) !== null) {
    const [, type, label, icon, urlOrAction, color] = match;
    actions.push({
      type: type as 'navigation' | 'action' | 'external',
      label,
      icon,
      url: type === 'external' || type === 'navigation' ? urlOrAction : undefined,
      action: type === 'action' ? urlOrAction : undefined,
      color: color || 'primary'
    });
  }

  return actions;
}

function generateContextualSuggestions(userContext: any): string[] {
  const suggestions: string[] = [];

  if (!userContext.is_authenticated) {
    suggestions.push(
      "How do I sign up for ZbInnovation?",
      "What are the benefits of joining the platform?",
      "Tell me about the innovation community",
      "How does the platform help innovators?"
    );
  } else {
    // Authenticated user suggestions based on context
    if (userContext.profile_completion < 50) {
      suggestions.push("How can I complete my profile?");
    }

    switch (userContext.current_page) {
      case 'dashboard':
        suggestions.push(
          "How do I create my first post?",
          "Show me my recent activity",
          "How do I connect with other innovators?",
          "What should I do next on the platform?"
        );
        break;
      case 'community':
        suggestions.push(
          "Help me find relevant groups to join",
          "What events are coming up?",
          "How do I share my innovation story?",
          "How can I connect with mentors?"
        );
        break;
      case 'profile':
        suggestions.push(
          "How can I improve my profile visibility?",
          "What should I include in my bio?",
          "How do I showcase my innovations?",
          "How do I attract the right connections?"
        );
        break;
      default:
        suggestions.push(
          "What can I do on this platform?",
          "How do I get started?",
          "Show me platform features",
          "Help me navigate the platform"
        );
    }

    // Profile type specific suggestions
    switch (userContext.profile_type) {
      case 'innovator':
        suggestions.push("How do I find investors for my innovation?");
        break;
      case 'investor':
        suggestions.push("How do I discover promising innovations?");
        break;
      case 'mentor':
        suggestions.push("How do I offer mentorship to innovators?");
        break;
    }
  }

  return suggestions.slice(0, 4); // Limit to 4 suggestions
}

function generateFallbackResponse(errorMessage: string): AIResponse {
  return {
    response: `I apologize, but I'm experiencing technical difficulties at the moment. However, I'm here to help you navigate the ZbInnovation platform and connect with Zimbabwe's innovation community. 

While I work on resolving this issue, you can explore the platform features using the buttons below, or try asking your question again in a moment.`,
    actions: [
      {
        type: 'navigation',
        label: 'Explore Community',
        icon: 'groups',
        url: '/virtual-community?tab=feed',
        color: 'primary'
      },
      {
        type: 'navigation',
        label: 'View Dashboard',
        icon: 'dashboard',
        url: '/dashboard',
        color: 'secondary'
      }
    ],
    suggestions: [
      "Try asking your question again",
      "How do I get started on the platform?",
      "What features are available?",
      "Help me navigate ZbInnovation"
    ],
    conversation_id: crypto.randomUUID()
  };
}
