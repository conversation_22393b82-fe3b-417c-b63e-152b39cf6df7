<template>
  <div class="ai-chat-container">
    <!-- Chat Toggle Button -->
    <q-btn
      v-if="!isOpen"
      @click="toggleChat"
      fab
      color="primary"
      icon="psychology"
      class="chat-toggle-btn"
      :class="{
        'pulse': hasUnreadMessage,
        'landing-position': isLandingPage,
        'community-position': isCommunityPage
      }"
    >
      <q-tooltip class="bg-primary">AI Assistant</q-tooltip>
    </q-btn>

    <!-- Chat Window -->
    <q-card v-if="isOpen" class="chat-window" :class="{
      'landing-position': isLandingPage,
      'community-position': isCommunityPage
    }">
      <!-- Header -->
      <q-card-section class="chat-header bg-primary text-white">
        <div class="row items-center justify-between">
          <div class="row items-center">
            <q-icon name="psychology" size="24px" class="q-mr-sm" />
            <div>
              <div class="text-weight-bold">ZbInnovation AI</div>
              <div class="text-caption">Your Innovation Assistant</div>
            </div>
          </div>
          <q-btn
            @click="closeChat"
            flat
            round
            icon="close"
            size="sm"
            class="text-white"
          />
        </div>
      </q-card-section>

      <!-- Messages Area -->
      <q-card-section class="chat-messages" ref="messagesContainer">
        <div v-if="messages.length === 0" class="welcome-message">
          <q-icon name="waving_hand" size="32px" color="primary" class="q-mb-sm" />
          <p class="text-body1 q-mb-sm">
            Hello! I'm your ZbInnovation AI Assistant. I'm here to help you navigate the platform, 
            connect with the right people, and grow your innovation journey.
          </p>
          <p class="text-body2 text-grey-7">
            Ask me about matchmaking, funding opportunities, or anything related to innovation in Zimbabwe!
          </p>
        </div>

        <div
          v-for="(message, index) in messages"
          :key="index"
          class="message"
          :class="{ 'user-message': message.role === 'user', 'ai-message': message.role === 'assistant' }"
        >
          <div class="message-content">
            <div v-if="message.role === 'assistant'" class="ai-avatar">
              <q-icon name="smart_toy" size="20px" />
            </div>
            <div class="message-text" v-html="formatMessage(message.content)"></div>
            <div v-if="message.role === 'user'" class="user-avatar">
              <q-icon name="person" size="20px" />
            </div>
          </div>
        </div>

        <!-- Typing Indicator -->
        <div v-if="isTyping" class="message ai-message">
          <div class="message-content">
            <div class="ai-avatar">
              <q-icon name="psychology" size="20px" />
            </div>
            <div class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </q-card-section>

      <!-- Input Area -->
      <q-card-section class="chat-input">
        <q-form @submit.prevent="sendMessage" class="row q-gutter-sm">
          <q-input
            v-model="currentMessage"
            placeholder="Ask me anything about innovation..."
            outlined
            dense
            class="col"
            :disable="isTyping"
            @keyup.enter="sendMessage"
          />
          <q-btn
            @click="sendMessage"
            color="primary"
            icon="send"
            round
            :disable="!currentMessage.trim() || isTyping"
            :loading="isTyping"
          />
        </q-form>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted, computed } from 'vue';
import { useQuasar } from 'quasar';
import { useRoute } from 'vue-router';
import { supabase } from '../../lib/supabase';
import { useAuthStore } from '../../stores/auth';

interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

// Composables
const $q = useQuasar();
const route = useRoute();
const authStore = useAuthStore();

// Reactive state
const isOpen = ref(false);
const messages = ref<ChatMessage[]>([]);
const currentMessage = ref('');
const isTyping = ref(false);
const hasUnreadMessage = ref(false);
const messagesContainer = ref<HTMLElement>();

// Position adjustment based on current route
const needsPositionAdjustment = computed(() => {
  const currentPath = route.path;
  // Adjust position on landing page (news ticker) and community pages (create post button)
  return currentPath === '/' ||
         currentPath.includes('/virtual-community') ||
         currentPath.includes('/community');
});

// Check if we're on landing page (for news ticker positioning)
const isLandingPage = computed(() => {
  return route.path === '/';
});

// Check if we're on community page (for create post button positioning)
const isCommunityPage = computed(() => {
  return route.path.includes('/virtual-community') || route.path.includes('/community');
});

// Methods
const toggleChat = () => {
  isOpen.value = !isOpen.value;
  hasUnreadMessage.value = false;

  if (isOpen.value) {
    nextTick(() => {
      scrollToBottom();
    });
  }
};

const closeChat = () => {
  isOpen.value = false;
  hasUnreadMessage.value = false;
};

const sendMessage = async () => {
  if (!currentMessage.value.trim() || isTyping.value) return;

  const userMessage: ChatMessage = {
    role: 'user',
    content: currentMessage.value.trim(),
    timestamp: new Date()
  };

  messages.value.push(userMessage);
  const messageToSend = currentMessage.value.trim();
  currentMessage.value = '';
  isTyping.value = true;

  await nextTick();
  scrollToBottom();

  try {
    // Call the AI chat Edge Function
    const { data, error } = await supabase.functions.invoke('ai-chat', {
      body: {
        message: messageToSend,
        conversation_history: messages.value.slice(-10).map(msg => ({
          role: msg.role,
          content: msg.content
        })),
        user_context: {
          profile_type: authStore.user?.user_metadata?.profile_type,
          user_id: authStore.user?.id
        }
      }
    });

    console.log('AI Response:', { data, error });

    if (error) {
      console.error('Supabase function error:', error);
      throw new Error(error.message || 'Failed to call AI function');
    }

    if (!data || !data.response) {
      throw new Error(data?.error || 'No response from AI service');
    }

    const aiMessage: ChatMessage = {
      role: 'assistant',
      content: data.response,
      timestamp: new Date()
    };

    messages.value.push(aiMessage);

    if (!isOpen.value) {
      hasUnreadMessage.value = true;
    }

  } catch (error: any) {
    console.error('AI Chat error:', error);

    // Provide a helpful fallback response
    let fallbackContent = '';
    if (messageToSend.toLowerCase().includes('hello') || messageToSend.toLowerCase().includes('hi')) {
      fallbackContent = "Hello! I'm your ZbInnovation AI Assistant. I'm here to help you navigate the platform, find connections, and grow your innovation journey. How can I assist you today?";
    } else if (messageToSend.toLowerCase().includes('help')) {
      fallbackContent = "I can help you with:\n• Finding the right connections and collaborators\n• Understanding platform features\n• Innovation and entrepreneurship guidance\n• Zimbabwe's innovation ecosystem insights\n\nWhat specific area would you like help with?";
    } else {
      fallbackContent = `I apologize, but I'm currently experiencing technical difficulties. However, I'm here to help with platform navigation, connections, and innovation guidance. Please try asking your question again, or contact support if the issue persists.`;
    }

    const errorMessage: ChatMessage = {
      role: 'assistant',
      content: fallbackContent,
      timestamp: new Date()
    };

    messages.value.push(errorMessage);

    // Only show technical error to developers
    if (process.env.NODE_ENV === 'development') {
      $q.notify({
        type: 'negative',
        message: `AI Error: ${error.message || 'Failed to get AI response'}`,
        position: 'top'
      });
    }
  } finally {
    isTyping.value = false;
    await nextTick();
    scrollToBottom();
  }
};

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

const formatMessage = (content: string) => {
  // Simple formatting for better readability
  return content
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>');
};

// Lifecycle
onMounted(() => {
  // Load chat history from localStorage if available
  const savedMessages = localStorage.getItem('ai-chat-history');
  if (savedMessages) {
    try {
      const parsed = JSON.parse(savedMessages);
      messages.value = parsed.map((msg: any) => ({
        ...msg,
        timestamp: new Date(msg.timestamp)
      }));
    } catch (error) {
      console.error('Failed to load chat history:', error);
    }
  }
});

// Save chat history when messages change
const saveMessages = () => {
  localStorage.setItem('ai-chat-history', JSON.stringify(messages.value));
};

// Watch for message changes to save history
import { watch } from 'vue';
watch(messages, saveMessages, { deep: true });
</script>

<style scoped>
.ai-chat-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9999; /* Much higher to be above all Quasar navigation components */
}

.chat-toggle-btn {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Position adjustments for landing page (above news ticker) */
.chat-toggle-btn.landing-position {
  bottom: 47px; /* Reduced by 50%: was 95px, now 47px */
  right: 5px; /* Reduced by 50%: was 10px, now 5px */
}

.chat-window.landing-position {
  bottom: 47px; /* Same positioning as button */
  right: 5px;
}

/* Position adjustments for community page (above create post button) */
.chat-toggle-btn.community-position {
  bottom: 40px; /* Reduced by 50%: was 80px, now 40px */
  right: 10px; /* Reduced by 50%: was 20px, now 10px */
}

.chat-window.community-position {
  bottom: 40px; /* Same positioning as button */
  right: 10px;
}

.chat-toggle-btn.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(25, 118, 210, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(25, 118, 210, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(25, 118, 210, 0);
  }
}

.chat-window {
  width: 380px;
  height: 500px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  border-radius: 12px;
  overflow: hidden;
  z-index: 9999; /* Ensure chat window is above everything including navigation */
}

.chat-header {
  padding: 16px;
  border-radius: 0;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #f8f9fa;
}

.welcome-message {
  text-align: center;
  padding: 20px;
  color: #666;
}

.message {
  margin-bottom: 16px;
}

.message-content {
  display: flex;
  align-items: flex-end;
  gap: 8px;
}

.user-message .message-content {
  flex-direction: row-reverse;
}

.message-text {
  max-width: 80%;
  padding: 12px 16px;
  border-radius: 18px;
  line-height: 1.4;
}

.user-message .message-text {
  background: #1976d2;
  color: white;
  border-bottom-right-radius: 4px;
}

.ai-message .message-text {
  background: white;
  color: #333;
  border: 1px solid #e0e0e0;
  border-bottom-left-radius: 4px;
}

.ai-avatar, .user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.ai-avatar {
  background: #e3f2fd;
  color: #1976d2;
}

.user-avatar {
  background: #1976d2;
  color: white;
}

.typing-indicator {
  display: flex;
  gap: 4px;
  padding: 12px 16px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 18px;
  border-bottom-left-radius: 4px;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #bbb;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.chat-input {
  padding: 16px;
  background: white;
  border-top: 1px solid #e0e0e0;
}

/* Mobile responsiveness */
@media (max-width: 480px) {
  .ai-chat-container {
    bottom: 10px;
    right: 10px;
  }

  .chat-toggle-btn.landing-position {
    bottom: 42px; /* Reduced by 50%: was 85px, now 42px */
    right: 5px; /* Reduced by 50%: was 10px, now 5px */
  }

  .chat-toggle-btn.community-position {
    bottom: 37px; /* Reduced by 50%: was 75px, now 37px */
    right: 7px; /* Reduced by 50%: was 15px, now 7px */
  }

  .chat-window {
    width: calc(100vw - 20px);
    height: calc(100vh - 120px);
    bottom: 10px;
    right: 10px;
  }

  .chat-window.landing-position {
    bottom: 42px; /* Match button position */
    right: 5px;
    height: calc(100vh - 127px); /* Adjust height for mobile with new positioning */
  }

  .chat-window.community-position {
    bottom: 37px; /* Match button position */
    right: 7px;
    height: calc(100vh - 122px); /* Adjust height for mobile community page */
  }
}
</style>
